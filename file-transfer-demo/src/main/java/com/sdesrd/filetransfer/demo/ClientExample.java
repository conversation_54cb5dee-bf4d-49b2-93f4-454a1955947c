package com.sdesrd.filetransfer.demo;

import java.io.File;
import java.util.concurrent.CompletableFuture;

import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.sdesrd.filetransfer.client.FileTransferClient;
import com.sdesrd.filetransfer.client.config.ClientConfig;
import com.sdesrd.filetransfer.client.config.ClientConfigBuilder;
import com.sdesrd.filetransfer.client.dto.DownloadResult;
import com.sdesrd.filetransfer.client.dto.TransferProgress;
import com.sdesrd.filetransfer.client.dto.UploadResult;
import com.sdesrd.filetransfer.client.listener.TransferListener;
import com.sdesrd.filetransfer.client.util.FileUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 文件传输客户端使用示例
 * 
 * 演示如何使用文件传输客户端SDK进行文件上传和下载
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "demo.client.enabled", havingValue = "true", matchIfMissing = true)
public class ClientExample implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        // 等待服务启动完成
        Thread.sleep(3000);
        
        log.info("开始演示文件传输客户端SDK的使用...");
        
        // 使用配置构建器创建客户端配置（推荐方式）
        ClientConfig config = ClientConfigBuilder.localConfig("demo", "demo-secret-key-2024");

        // 或者使用链式调用进行自定义配置
        // ClientConfig config = ClientConfigBuilder.create()
        //         .serverAddr("localhost")
        //         .serverPort(49011)
        //         .contextPath("") // 确保没有额外的上下文路径
        //         .auth("demo", "demo-secret-key-2024")
        //         .chunkSize(1024 * 1024) // 1MB 分块
        //         .maxConcurrentTransfers(3)
        //         .retry(3, 1000)
        //         .build();

        // 创建客户端（使用修复后的原始SDK）
        FileTransferClient client = new FileTransferClient(config);
        
        try {
            // 演示上传
            demonstrateUpload(client);

            // 演示下载（暂时注释掉）
            // demonstrateDownload(client);

        } finally {
            // 关闭客户端
            client.close();
        }
        
        log.info("文件传输客户端SDK演示完成！");
    }
    
    /**
     * 演示文件上传
     */
    private void demonstrateUpload(FileTransferClient client) {
        log.info("=== 演示文件上传 ===");
        
        // 创建一个测试文件
        String testFilePath = createTestFile();
        
        // 创建传输监听器
        TransferListener listener = new TransferListener() {
            @Override
            public void onStart(TransferProgress progress) {
                log.info("开始上传文件: {} ({})", 
                    progress.getFileName(), 
                    FileUtils.formatFileSize(progress.getTotalSize()));
            }
            
            @Override
            public void onProgress(TransferProgress progress) {
                log.info("上传进度: {}% ({}/{}) 速度: {}", 
                    String.format("%.2f", progress.getProgress()),
                    FileUtils.formatFileSize(progress.getTransferredSize()),
                    FileUtils.formatFileSize(progress.getTotalSize()),
                    progress.getSpeed() != null ? FileUtils.formatSpeed(progress.getSpeed()) : "计算中...");
            }
            
            @Override
            public void onCompleted(TransferProgress progress) {
                log.info("文件上传完成: {} ({})", 
                    progress.getFileName(), 
                    FileUtils.formatFileSize(progress.getTotalSize()));
            }
            
            @Override
            public void onError(TransferProgress progress, Throwable error) {
                log.error("文件上传失败: {}", progress.getFileName(), error);
            }
        };
        
        try {
            // 异步上传文件
            CompletableFuture<UploadResult> future = client.uploadFile(testFilePath, null, listener);
            
            // 等待上传完成
            UploadResult result = future.get();
            
            if (result.isSuccess()) {
                log.info("上传成功! 文件ID: {}, 传输ID: {}, 秒传: {}", 
                    result.getFileId(), 
                    result.getTransferId(), 
                    result.isFastUpload() ? "是" : "否");
            } else {
                log.error("上传失败: {}", result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("上传文件时发生异常", e);
        }
    }
    
    /*
     * 演示文件下载（暂时注释掉）
     */
    /*
    private void demonstrateDownload(FixedFileTransferClient client) {
        log.info("=== 演示文件下载 ===");

        // 这里需要一个已存在的文件ID
        String fileId = "your-file-id-here";
        String savePath = "./downloads/downloaded-file.txt";

        // 创建传输监听器
        TransferListener listener = new TransferListener() {
            @Override
            public void onStart(TransferProgress progress) {
                log.info("开始下载文件: {} ({})",
                    progress.getFileName(),
                    FileUtils.formatFileSize(progress.getTotalSize()));
            }

            @Override
            public void onProgress(TransferProgress progress) {
                log.info("下载进度: {}% ({}/{}) 速度: {}",
                    String.format("%.2f", progress.getProgress()),
                    FileUtils.formatFileSize(progress.getTransferredSize()),
                    FileUtils.formatFileSize(progress.getTotalSize()),
                    progress.getSpeed() != null ? FileUtils.formatSpeed(progress.getSpeed()) : "计算中...");
            }

            @Override
            public void onCompleted(TransferProgress progress) {
                log.info("文件下载完成: {}", progress.getFileName());
            }

            @Override
            public void onError(TransferProgress progress, Throwable error) {
                log.error("文件下载失败", error);
            }
        };

        try {
            // 异步下载文件
            CompletableFuture<DownloadResult> future = client.downloadFile(fileId, savePath, listener);

            // 等待下载完成
            DownloadResult result = future.get();

            if (result.isSuccess()) {
                log.info("下载成功! 保存路径: {}, 文件大小: {}",
                    result.getLocalPath(),
                    FileUtils.formatFileSize(result.getFileSize()));
            } else {
                log.error("下载失败: {}", result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("下载文件时发生异常", e);
        }
    }
    */
    
    /**
     * 创建测试文件
     */
    private String createTestFile() {
        try {
            File testFile = new File("./test-upload.txt");
            if (!testFile.exists()) {
                // 创建一个测试文件
                StringBuilder content = new StringBuilder();
                for (int i = 0; i < 1000; i++) {
                    content.append("这是第 ").append(i + 1).append(" 行测试数据，用于演示文件传输功能。\n");
                }
                
                java.nio.file.Files.write(testFile.toPath(), content.toString().getBytes("UTF-8"));
                log.info("创建测试文件: {} ({})", testFile.getAbsolutePath(), FileUtils.formatFileSize(testFile.length()));
            }
            
            return testFile.getAbsolutePath();
            
        } catch (Exception e) {
            log.error("创建测试文件失败", e);
            throw new RuntimeException("创建测试文件失败", e);
        }
    }
} 